const admin = require("firebase-admin");
const { onRequest } = require("firebase-functions/v2/https");
const { onCall } = require("firebase-functions/v2/https");
const { onSchedule } = require("firebase-functions/v2/scheduler");
const { Timestamp } = require("firebase-admin/firestore");
const { onDocumentCreated, onDocumentUpdated } = require("firebase-functions/v2/firestore");
const logger = require("firebase-functions/logger");
const crypto = require("crypto");

admin.initializeApp();

// Utility function to generate unique ID (only for nested objects like visits)
function generateId() {
  return Math.random().toString(36).substring(2, 11);
}

// Helper function to format date to YYYY-MM-DD HH:mm without timezone conversion
function formatDateTimeLocal(date) {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, "0");
  const day = String(date.getDate()).padStart(2, "0");
  const hours = String(date.getHours()).padStart(2, "0");
  const minutes = String(date.getMinutes()).padStart(2, "0");

  return `${year}-${month}-${day} ${hours}:${minutes}`;
}

function formatDateLocal(date) {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, "0");
  const day = String(date.getDate()).padStart(2, "0");

  return `${year}-${month}-${day}`;
}

function parseDateTimeLocal(dateTimeString) {
  const [datePart, timePart] = dateTimeString.split(" ");
  const [year, month, day] = datePart.split("-").map(Number);
  const [hours, minutes] = timePart.split(":").map(Number);

  // Create Date object using local timezone
  return new Date(year, month - 1, day, hours, minutes, 0, 0);
}

exports.createUser = onRequest({ cors: "*" }, async (req, res) => {
  // CHECK PAYLOAD
  const { email, password, ...rest } = req.body;
  if (!email) return res.status(400).send({ error: "email is missing" });
  if (!password) return res.status(400).send({ error: "password is missing" });
  if (!["NURSE", "CAREGIVER", "CLIENT"].includes(rest?.role))
    return res.status(400).send({ error: "role must be one of the following: NURSE, CARGIVER, CLIENT" });
  if (!rest?.name) return res.status(400).send({ error: "name is missing" });
  if (!["male", "female"].includes(rest?.gender))
    return res.status(400).send({ error: "gender must be one of the following: male, female" });

  try {
    // CREATE USER IN AUTH
    const userRecord = await admin.auth().createUser({
      email: email,
      password: password,
    });
    const uid = userRecord.uid;
    const userDoc = {
      createdAt: Timestamp.fromDate(new Date()),
      email,
      ...rest,
    };
    // SAVING DATA IN DB
    await admin.firestore().collection("Users").doc(uid).set(userDoc);
    if (rest?.role === "CLIENT") {
      upsertGeofence({
        description: "",
        coordinates: [userDoc?.address?.lng, userDoc?.address?.lat],
        metadata: {
          patient: uid,
        },
        userId: uid,
      });
    }
    return res.send({ user: { id: uid, ...userDoc } });
  } catch (error) {
    logger.error("ERROR >> ", error);
    return res.status(400).send(error);
  }
});

exports.sendNotificationOnCreate = onDocumentCreated("Notifications/{id}", async (event) => {
  const snapshot = event.data;
  if (!snapshot) {
    return;
  }
  const data = snapshot.data();
  let queryRef = admin.firestore().collection("Users");
  if (data.role !== "ALL") {
    queryRef = queryRef.where("role", "==", data.role);
  }
  const usersSnapshot = await queryRef.get();
  const tokens = usersSnapshot.docs.map((doc) => doc.data().fcmToken).filter((token) => token);

  const validTokens = tokens.filter((token) => token && typeof token === "string" && token.length > 0);
  await admin.firestore().collection("Notifications").doc(event.params.id).update({ tokens });

  if (validTokens.length > 0) {
    logger.info("No. of tokens >>", validTokens?.length);

    const message = {
      tokens: validTokens,
      notification: { body: data?.body, title: data?.title },
    };

    await admin
      .messaging()
      .sendEachForMulticast(message)
      .then(() => logger.info("Notification sent >>"))
      .catch((e) => logger.error("Error Sending Notification >> ", e));
  }
});

const upsertGeofence = async (payload) => {
  try {
    const { description = "", type = "circle", coordinates, radius = "50", metadata = {}, userId } = payload;

    if (!coordinates) {
      logger.info({ error: "Missing coordinates in body" });
      return;
    }

    const raw = JSON.stringify({
      coordinates,
      description,
      type,
      radius,
      metadata,
    });
    const radarRes = await fetch(`https://api.radar.io/v1/geofences/clients/${userId}`, {
      method: "PUT",
      body: raw,
      headers: {
        Authorization: "prj_test_sk_4383eab7442a52ffb4d8b831c088b01d32535fa8",
        "Content-Type": "application/json",
      },
    });

    if (!radarRes.ok) {
      const errorText = await radarRes.text();
      console.error("Radar API response failed:", radarRes.status, errorText);
      res.status(radarRes.status).json({
        success: false,
        error: "Radar API call failed",
        status: radarRes.status,
        details: errorText,
      });
      return;
    }
  } catch (error) {
    logger.error("Error in create geofence API:", error);
  }
};

exports.radarEventsWebhook = onRequest(async (req, res) => {
  if (req.method !== "POST") {
    res.status(405).send("Method Not Allowed");
    return;
  }

  const radarSigningId = req.headers["x-radar-signing-id"];
  const radarSignature = req.headers["x-radar-signature"];
  const RADAR_WEBHOOK_SECRET = "26d3134047bc082bafee6693f15e301cd572d9e6"; // store securely

  if (!radarSigningId || !radarSignature || !RADAR_WEBHOOK_SECRET) {
    res.status(400).send("Missing headers or secret");
    return;
  }

  // Compute HMAC-SHA1 signature
  const expectedSignature = crypto.createHmac("sha1", RADAR_WEBHOOK_SECRET).update(radarSigningId).digest("hex");

  // Compare signatures
  if (radarSignature !== expectedSignature) {
    res.status(401).send("Unauthorized: Signature mismatch");
    return;
  }

  try {
    const body = req.body;
    const { event } = body;
    console.log("event.type >>", event.type);
    if (event.type === "user.entered_geofence") {
      //
    }
    if (body.event.type === "user.exited_geofence") {
      //
    }
    res.status(200).json({
      success: true,
      message: `Event happened`,
    });
  } catch (error) {
    res.status(401).json({
      success: false,
      message: error.message,
    });
  }
});

function generateAppointmentDates(
  startDateTime,
  recurrenceType,
  recurrenceSettings = {},
  appointmentCount = null,
  recurrenceEndDate = null,
) {
  const appointments = [];
  const startDate = new Date(startDateTime);

  // Use recurrence end date if provided, otherwise fall back to 3 months for safety
  let endDate;
  if (recurrenceEndDate) {
    endDate = new Date(recurrenceEndDate);
    // Set end date to end of day to include appointments on the end date
    endDate.setHours(23, 59, 59, 999);
  } else {
    // Fallback to 3 months if no end date provided
    endDate = new Date(startDate);
    endDate.setMonth(endDate.getMonth() + 3);
  }

  // Use specific count if provided, otherwise use date-based logic
  const useCountLimit = appointmentCount && appointmentCount > 0;

  if (recurrenceType === "daily") {
    // Generate appointments for daily recurrence
    let currentDate = new Date(startDate);
    let count = 0;

    while (currentDate <= endDate && (!useCountLimit || count < appointmentCount)) {
      appointments.push(new Date(currentDate));
      currentDate.setDate(currentDate.getDate() + 1);
      count++;
    }
  } else if (recurrenceType === "weekly") {
    // Generate weekly appointments
    const { daysOfWeek = [] } = recurrenceSettings;
    const dayMap = { sun: 0, mon: 1, tue: 2, wed: 3, thu: 4, fri: 5, sat: 6 };

    if (daysOfWeek.length > 0) {
      const targetDays = daysOfWeek.map((day) => dayMap[day]).sort((a, b) => a - b);
      let currentDate = new Date(startDate);
      let count = 0;

      while (currentDate <= endDate && (!useCountLimit || count < appointmentCount)) {
        if (targetDays.includes(currentDate.getDay())) {
          appointments.push(new Date(currentDate));
          count++;
        }
        currentDate.setDate(currentDate.getDate() + 1);
      }
    } else {
      // Default to same day of week as start date
      let currentDate = new Date(startDate);
      let count = 0;

      while (currentDate <= endDate && (!useCountLimit || count < appointmentCount)) {
        appointments.push(new Date(currentDate));
        currentDate.setDate(currentDate.getDate() + 7);
        count++;
      }
    }
  } else if (recurrenceType === "monthly") {
    // Generate monthly appointments
    const { monthlyDates = [] } = recurrenceSettings;
    let currentDate = new Date(startDate);
    let count = 0;

    if (monthlyDates.length > 0) {
      // Use specific dates of the month
      while (currentDate <= endDate && (!useCountLimit || count < appointmentCount)) {
        for (const dateStr of monthlyDates) {
          const targetDate = parseInt(dateStr);
          const monthlyAppointment = new Date(currentDate.getFullYear(), currentDate.getMonth(), targetDate);

          // Set the time to match the original appointment
          monthlyAppointment.setHours(startDate.getHours(), startDate.getMinutes(), 0, 0);

          if (
            monthlyAppointment >= startDate &&
            monthlyAppointment <= endDate &&
            (!useCountLimit || count < appointmentCount)
          ) {
            appointments.push(new Date(monthlyAppointment));
            count++;
          }
        }
        currentDate.setMonth(currentDate.getMonth() + 1);
      }
    } else {
      // Default to same date of month as start date
      while (currentDate <= endDate && (!useCountLimit || count < appointmentCount)) {
        appointments.push(new Date(currentDate));
        currentDate.setMonth(currentDate.getMonth() + 1);
        count++;
      }
    }
  } else if (recurrenceType === "60-days") {
    // Generate appointments every 60 days
    let currentDate = new Date(startDate);
    let count = 0;

    while (currentDate <= endDate && (!useCountLimit || count < appointmentCount)) {
      appointments.push(new Date(currentDate));
      currentDate.setDate(currentDate.getDate() + 60);
      count++;
    }
  }

  return appointments;
}

// Cloud function to generate multiple appointments
exports.generateRecurringAppointments = onCall(async (request) => {
  try {
    const { appointmentData } = request.data;

    if (!appointmentData) {
      throw new Error("Appointment data is required");
    }

    const { recurrence, startDateTime, endDateTime, recurrenceEndDate, ...baseAppointmentData } = appointmentData;

    if (!recurrence || !recurrence.frequence) {
      // Create single appointment if no recurrence - let Firebase generate the ID
      const appointmentRef = admin.firestore().collection("Appointments").doc();
      const appointmentId = appointmentRef.id;

      const singleAppointment = {
        id: appointmentId,
        ...baseAppointmentData,
        startDateTime,
        endDateTime,
        startTimeStamp: Timestamp.fromDate(parseDateTimeLocal(startDateTime)),
        endTimeStamp: Timestamp.fromDate(parseDateTimeLocal(endDateTime)),
        serviceType: baseAppointmentData.serviceType || "auxiliary_nurse",
        recurrence: null,
        // NEW FIELDS FOR RECURRING APPOINTMENTS
        refID: appointmentId, // null for single appointments
        isRecurringInstance: false, // false for single appointments
        originalRecurrence: null, // null for single appointments
        // VISITS OBJECT - embedded visits data for each appointment
        visits: [
          {
            id: generateId(), // Firebase document ID for the visit
            date: startDateTime.split(" ")[0], // visit date (YYYY-MM-DD)
            clockIn: startDateTime, // scheduled start time
            clockOut: endDateTime, // scheduled end time
            status: "scheduled", // scheduled, completed, cancelled, missed
            notes: "", // visit notes
            createdAt: Timestamp.now(),
            updatedAt: Timestamp.now(), // when status was last updated
          },
        ],
        createdAt: Timestamp.now(),
        updatedAt: Timestamp.now(),
      };

      await appointmentRef.set(singleAppointment);

      return {
        success: true,
        appointmentIds: [appointmentId],
        count: 1,
      };
    }

    // Generate a unique ID for this recurring series - let Firebase generate it
    const recurringSeriesRef = admin.firestore().collection("Appointments").doc();
    const recurringSeriesId = recurringSeriesRef.id;

    // Generate appointment dates based on recurrence and end date
    const appointmentDates = generateAppointmentDates(
      startDateTime,
      recurrence.frequence,
      recurrence,
      null, // No count limit, use end date instead
      recurrenceEndDate, // Pass the recurrence end date
    );

    if (appointmentDates.length === 0) {
      throw new Error("No appointment dates generated. Please check your recurrence settings and end date.");
    }

    // Calculate appointment duration
    const startTime = parseDateTimeLocal(startDateTime);
    const endTime = parseDateTimeLocal(endDateTime);
    const duration = endTime.getTime() - startTime.getTime();

    const appointmentIds = [];
    const batch = admin.firestore().batch();

    // Create individual appointment documents
    for (const appointmentDate of appointmentDates) {
      // Let Firebase generate the appointment ID
      const appointmentRef = admin.firestore().collection("Appointments").doc();
      const appointmentId = appointmentRef.id;

      // Set the time to match original appointment
      const appointmentStart = new Date(appointmentDate);
      appointmentStart.setHours(startTime.getHours(), startTime.getMinutes(), 0, 0);

      const appointmentEnd = new Date(appointmentStart.getTime() + duration);

      const appointmentDoc = {
        id: appointmentId,
        ...baseAppointmentData,
        startDateTime: formatDateTimeLocal(appointmentStart), // YYYY-MM-DD HH:mm format
        endDateTime: formatDateTimeLocal(appointmentEnd), // YYYY-MM-DD HH:mm format
        startTimeStamp: Timestamp.fromDate(appointmentStart),
        endTimeStamp: Timestamp.fromDate(appointmentEnd),
        serviceType: baseAppointmentData.serviceType || "auxiliary_nurse",
        status: "SCHEDULED",
        recurrence: recurrence, // Store the full recurrence object
        recurrenceEndDate: recurrenceEndDate, // Store the end date for reference
        refID: recurringSeriesId,
        isRecurringInstance: true,
        originalRecurrence: recurrence,

        visits: [
          {
            id: generateId(),
            date: formatDateLocal(appointmentStart),
            clockIn: formatDateTimeLocal(appointmentStart),
            clockOut: formatDateTimeLocal(appointmentEnd),
            status: "scheduled",
            notes: "", // visit notes
            createdAt: Timestamp.now(),
            updatedAt: Timestamp.now(),
          },
        ],
        createdAt: Timestamp.now(),
        updatedAt: Timestamp.now(),
      };

      batch.set(appointmentRef, appointmentDoc);
      appointmentIds.push(appointmentId);
    }

    // Execute batch write
    await batch.commit();

    return {
      success: true,
      appointmentIds,
      count: appointmentIds.length,
      recurrenceType: recurrence.frequence,
      refID: recurringSeriesId,
      recurrenceEndDate: recurrenceEndDate,
    };
  } catch (error) {
    logger.error("Error in generateRecurringAppointments:", error);
    throw new Error(`Failed to generate appointments: ${error.message}`);
  }
});

exports.sendVisitNotesNotification = onCall(async (request) => {
  try {
    const { appointmentId, serviceName} = request.data;
    console.log("requested data-------",request.data);

    if (!appointmentId) {
      throw new Error("Appointment ID is required");
    }

    // Get appointment data
    const appointmentDoc = await admin.firestore().collection("Appointments").doc(appointmentId).get();

    if (!appointmentDoc.exists) {
      throw new Error("Appointment not found");
    }
   
    const afterData = appointmentDoc.data();

    // Check if visitNotes exist and have required fields
    if (!afterData.visitNotes) {
      console.log("No visitNotes found in appointment, skipping notification");
      return { success: false, error: "No visitNotes found" };
    }

    const visitNotes = afterData.visitNotes;
    const hasValidVisitNotes =
      visitNotes.completedServices &&
      Array.isArray(visitNotes.completedServices) &&
      visitNotes.completedServices.length > 0;

    if (!hasValidVisitNotes) {
      return { success: false, error: "No completedServices found or array is empty" };
    }


    // Get appointment data
    const clientId = afterData.client;
    const caregiverId = afterData.caregiver;

    if (!clientId) {
      console.log("No client ID found in appointment data");
      return { success: false, error: "No client ID found" };
    } // Fetch client data to get FCM token
    let fcmToken = null;
    try {
      const clientDoc = await admin.firestore().collection("Users").doc(clientId).get();
      if (clientDoc.exists) {
        const clientData = clientDoc.data();
        fcmToken = clientData.fcmToken;
        console.log(`Client FCM token found: ${fcmToken ? "Yes" : "No"}`);
      } else {
        console.log("Client document not found");
      }
    } catch (error) {
      console.error("Error fetching client data:", error);
    }

    // Fetch caregiver data to get name
    let caregiverName = "your caregiver";
    if (caregiverId) {
      try {
        const caregiverDoc = await admin.firestore().collection("Users").doc(caregiverId).get();
        if (caregiverDoc.exists) {
          const caregiverData = caregiverDoc.data();
          caregiverName = caregiverData.name || "your caregiver";
        }
      } catch (error) {
        console.error("Error fetching caregiver data:", error);
      }
    }

    // Create notification payload
    const notificationPayload = {
      title: "Service completed",
      body: `${serviceName} have been marked as completed by caregiver ${caregiverName}`,
      role: "CLIENT",
      type: "VISIT_NOTES_ADDED",
      read: false,
      receiverIds: [clientId],
      screen: "Notifications",
      visitNotes: visitNotes,
      metadata: {
        appointment: afterData,
      },
      createdAt: admin.firestore.FieldValue.serverTimestamp(),
    };

    // Save notification to Firestore
    const notificationRef = await admin.firestore().collection("Notifications").add(notificationPayload);
    console.log(`Notification saved to Firestore with ID: ${notificationRef.id}`);

    // Send FCM notification to the client
    if (fcmToken && typeof fcmToken === "string" && fcmToken.length > 0) {
      const message = {
        token: fcmToken,
        notification: {
          title: notificationPayload.title,
          body: notificationPayload.body,
        },
        data: {
          type: notificationPayload.type,
          appointmentId,
          clientId,
          screen: notificationPayload.screen,
          notificationId: notificationRef.id,
          appointment: afterData,
        },
      };

      try {
        const response = await admin.messaging().send(message);
        console.log(`FCM notification sent to client ${clientId}. Response:`, response);
      } catch (fcmError) {
        console.error("Error sending FCM notification:", fcmError);
      }
    } else {
      console.log("No valid FCM token found for client");
    }

    console.log(`Visit notes notification sent for appointment ${appointmentId} to client ${clientId}`);
    return {
      success: true,
      appointmentId: appointmentId,
      clientId: clientId,
      notificationId: notificationRef.id,
      notificationSent: true,
      fcmSent: !!fcmToken,
      visitNotes: visitNotes,
    };
  } catch (error) {
    console.error("Error sending visit notes notification:", error);
    throw new Error(`Failed to send visit notes notification: ${error.message}`);
  }
});

// exports.sendAppointmentCompletedNotification = onDocumentUpdated("Appointments/{appointmentId}", async (event) => {
//   try {
//     const appointmentId = event.params.appointmentId;
//     const beforeData = event.data.before.data();
//     const afterData = event.data.after.data();

//     const statusChanged = beforeData.status !== afterData.status;
//     const isCompleted = afterData.status?.toLowerCase() === "completed";

//     if (!statusChanged || !isCompleted) {
//       console.log("Appointment status not changed to completed, skipping notification");
//       return null;
//     }

//     const appointment = afterData;
//     const clientId = appointment.client;

//     if (!clientId) {
//       console.log("No client ID found in appointment");
//       return null;
//     }

//     // Get client/patient information
//     const clientDoc = await admin.firestore().collection("Users").doc(clientId).get();
//     if (!clientDoc.exists) {
//       console.log("Client not found");
//       return null;
//     }

//     const client = clientDoc.data();

//     // Format appointment date and time for display
//     const appointmentDate = new Date(appointment.startDateTime);
//     const formattedDate = appointmentDate.toLocaleDateString();
//     const formattedTime = appointmentDate.toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" });

//     // Create notification payload
//     const notificationPayload = {
//       title: "Appointment Completed",
//       body: `Your appointment on ${formattedDate} at ${formattedTime} has been completed. Thank you for choosing our services!`,
//       role: "CLIENT",
//       type: "APPOINTMENT_COMPLETED",
//       read: false,
//       receiverIds: [clientId],
//       metadata: {
//         appointmentId: appointmentId,
//         appointmentDate: appointment.startDateTime,
//         serviceType: appointment.serviceType || "healthcare service",
//       },
//       createdAt: admin.firestore.FieldValue.serverTimestamp(),
//     }; // Save notification to Firestore
//     await admin.firestore().collection("Notifications").add(notificationPayload);

//     // Send FCM notification directly to the client if they have a token
//     if (client.fcmToken && typeof client.fcmToken === "string" && client.fcmToken.length > 0) {
//       const message = {
//         token: client.fcmToken,
//         notification: {
//           title: notificationPayload.title,
//           body: notificationPayload.body,
//         },
//         data: {
//           type: notificationPayload.type,
//           appointmentId: appointmentId,
//           clientId: clientId,
//           serviceType: appointment.serviceType || "healthcare service",
//         },
//       };

//       try {
//         await admin.messaging().send(message);
//         console.log(`FCM notification sent to client ${clientId} for appointment completion`);
//       } catch (fcmError) {
//         console.error("Error sending FCM notification:", fcmError);
//       }
//     } else {
//       console.log("No valid FCM token found for client");
//     }

//     console.log(`Appointment completed notification sent for appointment ${appointmentId} to client ${clientId}`);

//     return {
//       success: true,
//       appointmentId: appointmentId,
//       clientId: [clientId],
//       notificationSent: true,
//       fcmSent: !!client.fcmToken,
//       status: afterData.status,
//     };
//   } catch (error) {
//     console.error("Error sending appointment completed notification:", error);
//     return {
//       success: false,
//       error: error.message,
//     };
//   }
// });

exports.sendAppointmentSubmittedNotification = onDocumentUpdated("Appointments/{appointmentId}", async (event) => {
  try {
    const appointmentId = event.params.appointmentId;
    const beforeData = event.data.before.data();
    const afterData = event.data.after.data();

    const statusChanged = beforeData.status !== afterData.status;
    const isSubmitted = afterData.status?.toLowerCase() === "submitted";

    if (!statusChanged || !isSubmitted) {
      console.log("Appointment status not changed to SUBMITTED, skipping notification");
      return null;
    }

    const clientId = afterData.client;
    const caregiverId = afterData.caregiver;

    if (!clientId) {
      console.log("No client ID found in appointment data");
      return { success: false, error: "No client ID found" };
    }

    // Fetch client data to get FCM token
    let fcmToken = null;
    try {
      const clientDoc = await admin.firestore().collection("Users").doc(clientId).get();
      if (clientDoc.exists) {
        const clientData = clientDoc.data();
        fcmToken = clientData.fcmToken;
        console.log(`Client FCM token found: ${fcmToken ? "Yes" : "No"}`);
      } else {
        console.log("Client document not found");
      }
    } catch (error) {
      console.error("Error fetching client data:", error);
    }

    // Create notification payload
    const notificationPayload = {
      title: "Appointment Completed",
      body: "Your appointment has been marked as completed by your caregiver.",
      role: "CLIENT",
      read: false,
      receiverIds: [clientId],
      screen: "Notifications",
      visitNotes: afterData.visitNotes || null,
      metadata: {
        appointmentId,
        caregiverId,
      },
      type: "APPOINTMENT_SUBMITTED",
      createdAt: admin.firestore.FieldValue.serverTimestamp(),
    };

    // Save notification to Firestore
    const notificationRef = await admin.firestore().collection("Notifications").add(notificationPayload);
    console.log(`Notification saved to Firestore with ID: ${notificationRef.id}`);

    // Send FCM notification to the client
    if (fcmToken && typeof fcmToken === "string" && fcmToken.length > 0) {
      const message = {
        token: fcmToken,
        notification: {
          title: notificationPayload.title,
          body: notificationPayload.body,
        },
        data: {
          type: notificationPayload.type,
          appointmentId,
          clientId,
          screen: notificationPayload.screen,
          notificationId: notificationRef.id,
        },
      };

      try {
        const response = await admin.messaging().send(message);
        console.log(`FCM notification sent to client ${clientId}. Response:`, response);
      } catch (fcmError) {
        console.error("Error sending FCM notification:", fcmError);
      }
    } else {
      console.log("No valid FCM token found for client");
    }

    return {
      success: true,
      appointmentId,
      clientId,
      notificationId: notificationRef.id,
      notificationSent: true,
      fcmSent: !!fcmToken,
    };
  } catch (error) {
    console.error("Error sending appointment submitted notification:", error);
    return {
      success: false,
      error: error.message,
    };
  }
});

// Cron job to check for late appointments every hour
exports.checkLateAppointments = onSchedule("0 * * * *", async (event) => {
  try {
    const now = new Date();
    const currentTimestamp = Timestamp.fromDate(now);

    const appointmentsRef = admin.firestore().collection("Appointments");

    const query = appointmentsRef.where("startTimeStamp", "<", currentTimestamp);

    const snapshot = await query.get();

    if (snapshot.empty) {
      return { processed: 0, message: "No late appointments found" };
    }

    const batch = admin.firestore().batch();
    let processedCount = 0;

    snapshot.forEach((doc) => {
      const appointmentData = doc.data();
      const appointmentId = doc.id;

      // Filter for SCHEDULED status in application code to avoid composite index
      if (appointmentData.status !== "SCHEDULED") {
        return; // Skip non-scheduled appointments
      }

      // Convert startTimeStamp to Date for logging
      let appointmentDateTime;
      if (appointmentData.startTimeStamp && appointmentData.startTimeStamp.toDate) {
        appointmentDateTime = appointmentData.startTimeStamp.toDate();
      } else {
        appointmentDateTime = new Date(appointmentData.startDateTime);
      }

      console.log(`Marking appointment ${appointmentId} as LATE. Scheduled time: ${appointmentDateTime.toISOString()}`);

      // Update appointment status to LATE
      batch.update(doc.ref, {
        status: "LATE",
        updatedAt: currentTimestamp,
        lateMarkedAt: currentTimestamp,
      });

      processedCount++;
    });

    // Commit all updates
    await batch.commit();

    console.log(`Successfully marked ${processedCount} appointments as LATE`);

    return {
      success: true,
      processed: processedCount,
      message: `Marked ${processedCount} appointments as LATE`,
      checkedAt: now.toISOString(),
    };
  } catch (error) {
    console.error("Error checking for late appointments:", error);
    throw new Error(`Failed to check for late appointments: ${error.message}`);
  }
});
