// components
import { depsOptions } from "@constants/options";
import Page from "@layout/Page";
import StaffList from "@widgets/StaffList";
import AssignAdminModal from "@components/AssignAdminModal";
import { useState } from "react";
import { useNavigate } from "react-router";
import { useSelector } from "react-redux";
import { Button, Box } from "@mui/material";
import { AdminPanelSettings } from "@mui/icons-material";

const Staff = () => {
  const navigate = useNavigate();
  const [category, setCategory] = useState(depsOptions[0]);
  const [isAssignAdminModalOpen, setIsAssignAdminModalOpen] = useState(false);

  const { user } = useSelector((state) => state.auth);

  const selectedRole = () => {
    switch (category.value) {
      case "all":
        return { btnText: "Add User", url: "/add_staff" };
      case "nurse":
        return { btnText: "Add User", url: "/add_staff"  };
      case "caregiver":
        return { btnText: "Add User", url: "/add_staff" };
      default:
        return { btnText: "Add User", url: "/add_staff" };
    }
  };

  return (
    <>
      <Page
        title="Staff List"
        btnText={selectedRole()?.btnText}
        showRightBtn
        onClickBtn={() => navigate(selectedRole()?.url)}
        customActions={
          user?.role === "ADMIN" ? (
            <Box sx={{ ml: 2 }}>
              <Button
                variant="outlined"
                startIcon={<AdminPanelSettings />}
                onClick={() => setIsAssignAdminModalOpen(true)}
                sx={{
                  borderColor: '#ff6b35',
                  color: '#ff6b35',
                  '&:hover': {
                    borderColor: '#e55a2b',
                    backgroundColor: 'rgba(255, 107, 53, 0.04)'
                  }
                }}
              >
                Assign Admin
              </Button>
            </Box>
          ) : null
        }
      >
        <StaffList variant="staff" category={category} setCategory={setCategory} />
      </Page>

      {/* Admin Assignment Modal */}
      <AssignAdminModal
        isVisible={isAssignAdminModalOpen}
        onClose={() => setIsAssignAdminModalOpen(false)}
      />
    </>
  );
};

export default Staff;
