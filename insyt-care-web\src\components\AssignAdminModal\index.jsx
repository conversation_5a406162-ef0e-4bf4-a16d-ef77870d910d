import React, { useState, useMemo } from "react";
import ModalWindow, { ModalContent } from "@components/ModalWindow";
import { 
  Button, 
  CircularProgress, 
  Divider, 
  Typography, 
  Box,
  Alert,
  Avatar,
  Chip
} from "@mui/material";
import { Controller, useForm } from "react-hook-form";
import styled from "styled-components";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { textSizes } from "@styles/vars";
import CustomSelect from "@ui/Select";
import { useSelector, useDispatch } from "react-redux";
import { assignAdminPermission } from "@store/slices/users";
import { useSnackbar } from "notistack";

const StyledModalContent = styled(ModalContent)`
  max-width: 600px;
  width: 100%;
`;

const UserCard = styled.div`
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  margin: 8px 0;
`;

const UserInfo = styled.div`
  flex: 1;
`;

const UserName = styled(Typography)`
  font-weight: 600;
  font-size: ${textSizes["14"]};
`;

const UserDetails = styled(Typography)`
  color: #666;
  font-size: ${textSizes["12"]};
`;

const assignAdminSchema = z.object({
  selectedUser: z.object({
    value: z.string(),
    label: z.string(),
    user: z.object({
      id: z.string(),
      name: z.string(),
      email: z.string(),
      role: z.string(),
    }),
  }).nullable().refine((val) => val !== null, {
    message: "Please select a user to promote to admin",
  }),
});

const AssignAdminModal = ({ isVisible, onClose }) => {
  const dispatch = useDispatch();
  const { enqueueSnackbar } = useSnackbar();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [confirmationStep, setConfirmationStep] = useState(false);

  const { nurses, caregivers, clients } = useSelector((state) => state.users);
  const { user: currentUser } = useSelector((state) => state.auth);

  const {
    handleSubmit,
    control,
    formState: { errors },
    reset,
    watch,
  } = useForm({
    defaultValues: {
      selectedUser: null,
    },
    resolver: zodResolver(assignAdminSchema),
    mode: "onChange",
  });

  const selectedUser = watch("selectedUser");

  // Create user options for the select dropdown
  const userOptions = useMemo(() => {
    const allUsers = [...nurses, ...caregivers, ...clients];

    const filteredUsers = allUsers.filter((user) => user.role !== "ADMIN" && user.id !== currentUser?.id);

    const options = filteredUsers.map((user) => ({
      value: user.id,
      label: `${user.name} (${user.email})`,
      user: {
        id: user.id,
        name: user.name,
        email: user.email,
        role: user.role,
      },
    }));

    return options;
  }, [nurses, caregivers, clients, currentUser]);

  const handleClose = () => {
    reset();
    setConfirmationStep(false);
    setIsSubmitting(false);
    onClose();
  };

  const onSubmit = async (data) => {
    if (!confirmationStep) {
      setConfirmationStep(true);
      return;
    }

    setIsSubmitting(true);
    
    try {
      await dispatch(assignAdminPermission({
        userId: data.selectedUser.user.id,
        userEmail: data.selectedUser.user.email,
        userName: data.selectedUser.user.name,
      })).unwrap();

      enqueueSnackbar(
        `Successfully assigned admin permissions to ${data.selectedUser.user.name}`,
        { variant: "success" }
      );
      
      handleClose();
    } catch (error) {
      console.error("Error assigning admin permission:", error);

      let errorMessage = "Failed to assign admin permissions";

      // Handle specific error scenarios
      if (error?.response?.status === 403) {
        errorMessage = "You don't have permission to assign admin roles";
      } else if (error?.response?.status === 404) {
        errorMessage = "User not found";
      } else if (error?.response?.status === 409) {
        errorMessage = "User already has admin permissions";
      } else if (error?.response?.data?.message) {
        errorMessage = error.response.data.message;
      } else if (error?.message) {
        errorMessage = error.message;
      } else if (error?.code === "auth/insufficient-permission") {
        errorMessage = "Insufficient permissions to perform this action";
      } else if (error?.code === "auth/user-not-found") {
        errorMessage = "The selected user could not be found";
      }

      enqueueSnackbar(errorMessage, { variant: "error" });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleBack = () => {
    setConfirmationStep(false);
  };

  const getRoleColor = (role) => {
    switch (role) {
      case "NURSE":
        return "primary";
      case "CAREGIVER":
        return "secondary";
      case "CLIENT":
        return "default";
      default:
        return "default";
    }
  };

  // Authorization check - only allow admin users
  if (currentUser?.role !== "ADMIN") {
    return (
      <ModalWindow isVisible={isVisible} visibilityHandler={handleClose}>
        <StyledModalContent>
          <Typography variant="h6" component="h2" gutterBottom>
            Access Denied
          </Typography>
          <Divider sx={{ mb: 2 }} />
          <Alert severity="error">
            You don't have permission to assign admin roles. Only existing admin users can perform this action.
          </Alert>
          <Box display="flex" justifyContent="flex-end" mt={3}>
            <Button variant="outlined" onClick={handleClose}>
              Close
            </Button>
          </Box>
        </StyledModalContent>
      </ModalWindow>
    );
  }

  return (
    <ModalWindow isVisible={isVisible} visibilityHandler={handleClose}>
      <StyledModalContent>
        <Typography variant="h6" component="h2" gutterBottom>
          {confirmationStep ? "Confirm Admin Assignment" : "Assign Admin Permissions"}
        </Typography>
        
        <Divider sx={{ mb: 2 }} />

        {!confirmationStep ? (
          <form onSubmit={handleSubmit(onSubmit)}>
            <Box mb={3}>
              <Typography variant="body2" color="text.secondary" gutterBottom>
                Select a user to promote to admin status. Only non-admin users are shown.
              </Typography>

              {userOptions.length === 0 ? (
                <Alert severity="info" sx={{ mt: 1 }}>
                  No users available to promote to admin. All users may already be admins or no users are loaded.
                  <br />
                  <small>
                    Debug: Nurses: {nurses.length}, Caregivers: {caregivers.length}, Clients: {clients.length}
                  </small>
                </Alert>
              ) : (
                <Controller
                  name="selectedUser"
                  control={control}
                  render={({ field: { onChange, value, name } }) => (
                    <CustomSelect
                      value={value}
                      changeHandler={(selectedOption) => onChange(selectedOption)}
                      options={userOptions}
                      placeholder="Search and select a user..."
                      isSearchable
                      isClearable
                      variant="basic"
                      label={name}
                    />
                  )}
                />
              )}

              {errors.selectedUser && (
                <Alert severity="error" sx={{ mt: 1 }}>
                  {errors.selectedUser.message}
                </Alert>
              )}
            </Box>

            {selectedUser && (
              <Box mb={3}>
                <Typography variant="subtitle2" gutterBottom>
                  Selected User:
                </Typography>
                <UserCard>
                  <Avatar sx={{ width: 40, height: 40 }}>
                    {selectedUser.user.name.charAt(0).toUpperCase()}
                  </Avatar>
                  <UserInfo>
                    <UserName>{selectedUser.user.name}</UserName>
                    <UserDetails>{selectedUser.user.email}</UserDetails>
                  </UserInfo>
                  <Chip 
                    label={selectedUser.user.role} 
                    color={getRoleColor(selectedUser.user.role)}
                    size="small"
                  />
                </UserCard>
              </Box>
            )}

            <Box display="flex" gap={2} justifyContent="flex-end">
              <Button 
                variant="outlined" 
                onClick={handleClose}
                disabled={isSubmitting}
              >
                Cancel
              </Button>
              <Button 
                type="submit" 
                variant="contained" 
                disabled={!selectedUser || isSubmitting}
              >
                Continue
              </Button>
            </Box>
          </form>
        ) : (
          <Box>
            <Alert severity="warning" sx={{ mb: 3 }}>
              <Typography variant="body2">
                <strong>Warning:</strong> You are about to assign admin permissions to this user. 
                Admin users will have full access to the system including the ability to manage all users, 
                appointments, and system settings.
              </Typography>
            </Alert>

            <Box mb={3}>
              <Typography variant="subtitle2" gutterBottom>
                User to be promoted:
              </Typography>
              <UserCard>
                <Avatar sx={{ width: 40, height: 40 }}>
                  {selectedUser.user.name.charAt(0).toUpperCase()}
                </Avatar>
                <UserInfo>
                  <UserName>{selectedUser.user.name}</UserName>
                  <UserDetails>{selectedUser.user.email}</UserDetails>
                  <UserDetails>Current Role: {selectedUser.user.role}</UserDetails>
                </UserInfo>
                <Chip 
                  label="Will become ADMIN" 
                  color="error"
                  size="small"
                />
              </UserCard>
            </Box>

            <Box display="flex" gap={2} justifyContent="flex-end">
              <Button 
                variant="outlined" 
                onClick={handleBack}
                disabled={isSubmitting}
              >
                Back
              </Button>
              <Button 
                variant="contained" 
                color="error"
                onClick={handleSubmit(onSubmit)}
                disabled={isSubmitting}
                startIcon={isSubmitting ? <CircularProgress size={20} /> : null}
              >
                {isSubmitting ? "Assigning..." : "Confirm Assignment"}
              </Button>
            </Box>
          </Box>
        )}
      </StyledModalContent>
    </ModalWindow>
  );
};

export default AssignAdminModal;
