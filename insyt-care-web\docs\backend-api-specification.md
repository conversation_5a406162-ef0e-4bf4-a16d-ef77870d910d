# Backend API Specification: Admin Permission Assignment

## Overview
This document specifies the backend API endpoint required to support the admin permission assignment feature in the Insyt Care web application.

## Endpoint: `/assignAdminPermission`

### Method
`POST`

### URL
`https://us-central1-insyt-care.cloudfunctions.net/assignAdminPermission`

### Authentication
- Requires Bearer token in Authorization header
- Token should be validated to ensure the requesting user exists and is authenticated

### Authorization
- Only users with role "ADMIN" can call this endpoint
- The endpoint must verify the requesting user's admin status before proceeding

### Request Body
```json
{
  "userId": "string",      // Required: ID of the user to promote to admin
  "userEmail": "string",   // Required: Email of the user (for verification)
  "userName": "string"     // Required: Name of the user (for logging/audit)
}
```

### Request Validation
1. **Authentication Check**: Verify the Bearer token is valid
2. **Authorization Check**: Ensure the requesting user has role "ADMIN"
3. **Input Validation**:
   - `userId` must be a non-empty string
   - `userEmail` must be a valid email format
   - `userName` must be a non-empty string
4. **User Existence Check**: Verify the target user exists in the database
5. **Role Check**: Ensure the target user is not already an admin

### Implementation Steps
1. **Extract and validate the Bearer token** from the Authorization header
2. **Get the requesting user** from the token and verify they have "ADMIN" role
3. **Validate the request payload** according to the schema above
4. **Check if the target user exists** in the Firestore Users collection
5. **Verify the target user is not already an admin** (role !== "ADMIN")
6. **Update the user's role** in Firestore to "ADMIN"
7. **Log the action** for audit purposes (optional but recommended)
8. **Return success response**

### Success Response (200)
```json
{
  "success": true,
  "message": "Admin permissions assigned successfully",
  "data": {
    "userId": "string",
    "previousRole": "string",
    "newRole": "ADMIN",
    "assignedBy": "string",
    "assignedAt": "ISO 8601 timestamp"
  }
}
```

### Error Responses

#### 400 - Bad Request
```json
{
  "success": false,
  "message": "Invalid request data",
  "error": "Specific validation error message"
}
```

#### 401 - Unauthorized
```json
{
  "success": false,
  "message": "Authentication required",
  "error": "Invalid or missing authorization token"
}
```

#### 403 - Forbidden
```json
{
  "success": false,
  "message": "Insufficient permissions",
  "error": "Only admin users can assign admin permissions"
}
```

#### 404 - Not Found
```json
{
  "success": false,
  "message": "User not found",
  "error": "The specified user does not exist"
}
```

#### 409 - Conflict
```json
{
  "success": false,
  "message": "User already has admin permissions",
  "error": "The specified user is already an admin"
}
```

#### 500 - Internal Server Error
```json
{
  "success": false,
  "message": "Internal server error",
  "error": "An unexpected error occurred"
}
```

### Security Considerations
1. **Rate Limiting**: Implement rate limiting to prevent abuse
2. **Audit Logging**: Log all admin assignment attempts (successful and failed)
3. **Input Sanitization**: Sanitize all input data to prevent injection attacks
4. **Token Validation**: Ensure tokens are properly validated and not expired
5. **Database Transactions**: Use transactions to ensure data consistency

### Database Schema Requirements
The Users collection in Firestore should have the following structure:
```javascript
{
  id: "user_id",
  role: "ADMIN" | "NURSE" | "CAREGIVER" | "CLIENT",
  email: "<EMAIL>",
  name: "User Name",
  // ... other user fields
}
```

### Example Implementation (Node.js/Firebase Functions)
```javascript
const functions = require('firebase-functions');
const admin = require('firebase-admin');

exports.assignAdminPermission = functions.https.onRequest(async (req, res) => {
  try {
    // 1. Validate method
    if (req.method !== 'POST') {
      return res.status(405).json({
        success: false,
        message: 'Method not allowed'
      });
    }

    // 2. Extract and validate token
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({
        success: false,
        message: 'Authentication required',
        error: 'Invalid or missing authorization token'
      });
    }

    const token = authHeader.split('Bearer ')[1];
    const decodedToken = await admin.auth().verifyIdToken(token);
    
    // 3. Get requesting user and verify admin role
    const requestingUserDoc = await admin.firestore()
      .collection('Users')
      .doc(decodedToken.uid)
      .get();
    
    if (!requestingUserDoc.exists || requestingUserDoc.data().role !== 'ADMIN') {
      return res.status(403).json({
        success: false,
        message: 'Insufficient permissions',
        error: 'Only admin users can assign admin permissions'
      });
    }

    // 4. Validate request body
    const { userId, userEmail, userName } = req.body;
    if (!userId || !userEmail || !userName) {
      return res.status(400).json({
        success: false,
        message: 'Invalid request data',
        error: 'userId, userEmail, and userName are required'
      });
    }

    // 5. Check if target user exists
    const targetUserDoc = await admin.firestore()
      .collection('Users')
      .doc(userId)
      .get();
    
    if (!targetUserDoc.exists) {
      return res.status(404).json({
        success: false,
        message: 'User not found',
        error: 'The specified user does not exist'
      });
    }

    const targetUser = targetUserDoc.data();
    
    // 6. Check if user is already admin
    if (targetUser.role === 'ADMIN') {
      return res.status(409).json({
        success: false,
        message: 'User already has admin permissions',
        error: 'The specified user is already an admin'
      });
    }

    // 7. Update user role
    const previousRole = targetUser.role;
    await admin.firestore()
      .collection('Users')
      .doc(userId)
      .update({
        role: 'ADMIN',
        updatedAt: admin.firestore.FieldValue.serverTimestamp()
      });

    // 8. Return success response
    res.status(200).json({
      success: true,
      message: 'Admin permissions assigned successfully',
      data: {
        userId,
        previousRole,
        newRole: 'ADMIN',
        assignedBy: decodedToken.uid,
        assignedAt: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('Error assigning admin permission:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: 'An unexpected error occurred'
    });
  }
});
```

### Testing Checklist
- [ ] Test with valid admin user and valid target user
- [ ] Test with non-admin user (should return 403)
- [ ] Test with invalid/missing token (should return 401)
- [ ] Test with non-existent target user (should return 404)
- [ ] Test with target user who is already admin (should return 409)
- [ ] Test with invalid request body (should return 400)
- [ ] Test error handling for database failures
- [ ] Verify user role is actually updated in database
- [ ] Test rate limiting (if implemented)
