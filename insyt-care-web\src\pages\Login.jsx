import React, { useState } from "react";
import {
  Checkbox,
  FormControl,
  FormControlLabel,
  Grid,
  IconButton,
  Button,
  CircularProgress,
  Box,
  Typography,
} from "@mui/material";
import { Controller, useForm } from "react-hook-form";
import z from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { VisibilityOff, Visibility, CheckBox, CheckBoxOutlineBlank } from "@mui/icons-material";
import { COLLECTIONS } from "@constants/app";
import { signInWithEmailAndPassword } from "firebase/auth";
import { app, auth, db, messaging } from "config/firebase.config";
import { doc, getDoc, updateDoc } from "firebase/firestore";
import { useNavigate } from "react-router";
import BG_IMAGE from "@assets/AUTH_LEFTSIDE_BG.png";
import SIGN_IN_LEFT_CARTOON from "@assets/SIGN_IN_LEFT_CARTOON.webp";
import LOGO from "@assets/LOGO_BG_WHITE_ROUND.png";
import styled from "styled-components";
import Field from "@ui/Field";
import { useSnackbar } from "notistack";
import { getMessaging, getToken, isSupported } from "firebase/messaging";
import { useEffect } from "react";

export const loginSchema = z.object({
  email: z.string().nonempty("Email is required").email("Must be a valid email"),
  password: z.string().nonempty("Password is required").min(6, "Password must have at least 6 characters"),
  agreedToTC: z.boolean().refine((val) => val === true, {
    message: "You must agree to the terms and conditions",
  }),
});

const StyledField = styled(Field)`
  background-color: #f5f5f5;
  width: 100%;
  padding: 24px 16px;
`;

const Login = () => {
  document.title = "Login - Insyt Care";
  const [showPassword, setShowPassword] = useState(false);
  const navigate = useNavigate();
  const { enqueueSnackbar } = useSnackbar();
  const [fcm, setFCM] = useState("");

  const {
    handleSubmit,
    formState: { errors, isDirty, isValid, isSubmitting },
    control,
  } = useForm({
    defaultValues: {
      email: "",
      password: "",
      agreedToTC: false,
    },
    mode: "all",
    resolver: zodResolver(loginSchema),
  });

  async function submitFormToLogin(formValues) {
    const { agreedToTC, ...rest } = formValues;
    await signInWithEmailAndPassword(auth, rest.email, rest.password)
      .then(async (res) => {
        const docSnapshot = await getDoc(doc(db, COLLECTIONS.USERS, res.user.uid));
        const user = docSnapshot.data();

        // HANDLE ADMIN
        if (user.role === "ADMIN") {
          navigate("/dashboard");
          localStorage.setItem("userId", res.user.uid);
        }

        // HANDLE NURSE
        else if (user.role === "NURSE") {
          localStorage.setItem("userId", res.user.uid);
          await updateDoc(doc(db, COLLECTIONS.USERS, res.user.uid), { fcmToken: fcm });
          navigate("/dashboard");
        }
      })
      .catch((error) => {
        console.error("SIGN IN > ", error);
        // INTERNET NOT WORKING
        if (error.code === "auth/network-request-failed") {
          enqueueSnackbar("Make sure your internet is working", { variant: "error" });
        }
        // USER NOT EXISTS
        else if (error.code === "auth/user-not-found") {
          enqueueSnackbar("Invalid email or password", { variant: "error" });
        }
        // INVAVLID CREDENTIALS
        else if (error.code === "auth/wrong-password" || "auth/invalid-login-credentials") {
          enqueueSnackbar("Invalid email or password", { variant: "error" });
        } else {
          enqueueSnackbar("Something went wrong", { variant: "error" });
        }
      });
  }

  async function getPermission() {
    if (await isSupported()) {
      const permission = await Notification.requestPermission();
      if (permission === "granted") {
        const tokenFCM = await getToken(messaging, {
          vapidKey: process.env.REACT_APP_FIREBASE_WEB_PUSH,
        });
        console.log("fcm >>", tokenFCM);

        setFCM(tokenFCM);
      } else if (permission === "denied") {
        alert("You've denied the notifications.");
      }
    }
  }

  useEffect(() => {
    getPermission();
  }, []);

  return (
    <>
      <Grid container justifyContent="center" alignItems="center" style={{ height: "100vh" }}>
        <Grid
          // md={6}
          // sm={12}
          size={{ xs: 12, md: 6 }}
          sx={{
            backgroundImage: `url(${BG_IMAGE})`,
            backgroundRepeat: "no-repeat",
            height: "100vh",
            backgroundSize: "cover",
            backgroundPosition: "center",
            p: 4,
            display: { xs: "none", sm: "none", md: "flex" },
            flexDirection: "column",
            justifyContent: "space-between",
          }}
        >
          <Box display="flex" flexDirection="column" gap={2}>
            <Box display="flex" justifyContent="start" alignItems="center" gap={1}>
              {/* <APP_LOGO fill={colors.blue} /> */}
              <img src={LOGO} alt="insyt care logo" style={{ objectFit: "contain", width: 42, height: 42 }} />
              <Typography textTransform="uppercase" color="#fff" fontWeight={600} variant="h6">
                Insytcare
              </Typography>
            </Box>
            <Typography textTransform="capitalize" color="#fff" fontWeight={600} variant="h5">
              Empowering Better Health Outcomes
            </Typography>
            <Typography textTransform="full-size-kana" color="#fff" fontWeight={400} variant="body1">
              We connect patients and healthcare professionals through a seamless, secure, and user-friendly platform
              that makes managing care simple and effective.
            </Typography>
          </Box>
          <img src={SIGN_IN_LEFT_CARTOON} alt="illustration" style={{ objectFit: "contain", maxHeight: 300 }} />
        </Grid>
        <Grid
          // md={6} sm={12}
          size={{ xs: 12, md: 6 }}
          sx={{
            backgroundColor: "white",
            padding: 4,
            height: "100vh",
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
          }}
        >
          <Box sx={{ maxWidth: { xs: "100%", lg: "70%" } }}>
            <Typography fontWeight={500} variant="h4" textAlign="center" display="block" mb={3}>
              Sign In
            </Typography>
            <form onSubmit={handleSubmit(submitFormToLogin)}>
              <Box display="flex" flexDirection="column" gap={2}>
                <Box position="relative">
                  <Controller
                    name="email"
                    control={control}
                    render={({ field }) => {
                      return <StyledField placeholder="Email" type="email" {...field} />;
                    }}
                  />
                  {errors?.email?.message && (
                    <Typography color="error" variant="caption">
                      {errors?.email?.message}
                    </Typography>
                  )}
                </Box>

                {/* PASSWORD */}
                <Box position="relative">
                  <Box position="relative">
                    <Controller
                      name="password"
                      control={control}
                      render={({ field }) => {
                        return (
                          <StyledField placeholder="Password" type={showPassword ? "text" : "password"} {...field} />
                        );
                      }}
                    />

                    <IconButton
                      onClick={() => setShowPassword(!showPassword)}
                      sx={{ position: "absolute", top: 0, bottom: 0, right: 2 }}
                    >
                      {showPassword ? <VisibilityOff /> : <Visibility />}
                    </IconButton>
                  </Box>
                  {errors?.password?.message && (
                    <Typography color="error" variant="caption">
                      {errors?.password?.message}
                    </Typography>
                  )}
                </Box>
              </Box>

              {/* AGREED TO T&c */}
              <FormControl sx={{ mt: 2 }}>
                <FormControlLabel
                  sx={{ fontSize: 16, ".MuiTypography-root": { fontSize: "12px" } }}
                  label={
                    <>By selecting Continue, you agree to our Terms of Service and acknowledge our Privacy Policy.</>
                  }
                  control={
                    <Controller
                      name="agreedToTC"
                      control={control}
                      render={({ field }) => (
                        <Checkbox
                          {...field}
                          color="primary"
                          checkedIcon={<CheckBox />}
                          icon={<CheckBoxOutlineBlank fill="#2D2D2D" opacity={0.5} />}
                          sx={{ mb: "12px" }}
                        />
                      )}
                    />
                  }
                />
              </FormControl>

              {/* SUBMIT BUTTON - CREATE ACCOUNT */}
              <FormControl sx={{ display: "block", marginTop: 2 }}>
                <Button
                  fullWidth
                  variant="contained"
                  color="primary"
                  type="submit"
                  sx={{
                    fontSize: 16,
                    borderRadius: 2,
                    py: 1.2,
                    textTransform: "none",
                    fontWeight: 400,
                  }}
                  disabled={isSubmitting || !isDirty || !isValid || Object.keys(errors).length > 0}
                >
                  {"Sign In"}
                  {isSubmitting ? <CircularProgress size={16} color="inherit" sx={{ marginLeft: 1 }} /> : null}
                </Button>
              </FormControl>
            </form>
          </Box>
        </Grid>
      </Grid>
    </>
  );
};

export default Login;
